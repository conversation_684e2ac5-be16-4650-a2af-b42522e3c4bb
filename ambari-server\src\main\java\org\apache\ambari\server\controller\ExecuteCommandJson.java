/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.ambari.server.controller;

/**
 * Wraps the strings needed when creating a stage for a custom command or action.
 */
public class ExecuteCommandJson {

  private String clusterHostJson;
  private String commandParamsJson;
  private String hostParamsJson;

  ExecuteCommandJson(String clusterHosts, String commandParams, String hostParams) {
    clusterHostJson = clusterHosts;
    commandParamsJson = commandParams;
    hostParamsJson = hostParams;
  }

  /**
   * @return the host params
   */
  public String getHostParamsForStage() {
    return hostParamsJson;
  }

  /**
   * @return the command params
   */
  public String getCommandParamsForStage() {
    return commandParamsJson;
  }

  /**
   * @return the cluster host info
   */
  public String getClusterHostInfo() {
    return clusterHostJson;
  }

  public void setClusterHostInfo(String clusterHostJson){
    this.clusterHostJson = clusterHostJson;
  }

}
