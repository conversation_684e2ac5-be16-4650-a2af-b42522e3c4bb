<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">
  <!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
  <parent>
    <groupId>org.apache.ambari</groupId>
    <artifactId>ambari-project</artifactId>
    <version>${revision}</version>
    <relativePath>../ambari-project</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.apache.ambari</groupId>
  <artifactId>ambari-agent</artifactId>
  <name>Ambari Agent</name>
  <description>Ambari Agent</description>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <final.name>${project.artifactId}-${project.version}</final.name>
    <package.release>1</package.release>
    <package.prefix>/usr</package.prefix>
    <package.log.dir>/var/log/ambari-agent</package.log.dir>
    <package.pid.dir>/var/run/ambari-agent</package.pid.dir>
    <skipTests>false</skipTests>
    <agent.install.dir>/usr/lib/ambari-agent/lib/ambari_agent</agent.install.dir>
    <ambari_commons.install.dir>/usr/lib/ambari-agent/lib/ambari_commons</ambari_commons.install.dir>
    <resource_management.install.dir>/usr/lib/ambari-agent/lib/resource_management</resource_management.install.dir>
    <jinja.install.dir>/usr/lib/ambari-agent/lib/ambari_jinja2</jinja.install.dir>
    <simplejson.install.dir>/usr/lib/ambari-agent/lib/ambari_simplejson</simplejson.install.dir>
    <stomp.install.dir>/usr/lib/ambari-agent/lib/ambari_stomp</stomp.install.dir>
    <ws4py.install.dir>/usr/lib/ambari-agent/lib/ambari_ws4py</ws4py.install.dir>
    <pbkdf2.install.dir>/usr/lib/ambari-agent/lib/ambari_pbkdf2</pbkdf2.install.dir>
    <pyaes.install.dir>/usr/lib/ambari-agent/lib/ambari_pyaes</pyaes.install.dir>
    <lib.dir>/usr/lib/ambari-agent/lib</lib.dir>
    <deb.architecture>amd64</deb.architecture>
    <ambari.server.module>../ambari-server</ambari.server.module>
    <target.cache.dir>${project.build.directory}/cache/</target.cache.dir>
    <resource.keeper.script>${ambari.server.module}/src/main/python/ambari_server/resourceFilesKeeper.py</resource.keeper.script>
    <resourceManagementSrcLocation>${project.basedir}/../ambari-common/src/main/python/resource_management</resourceManagementSrcLocation>
    <resourcesFolder>${ambari.server.module}/src/main/resources</resourcesFolder>
    <customActionsLocation>${target.cache.dir}/custom_actions</customActionsLocation>
    <empty.dir>src/packages/tarball</empty.dir> <!-- any directory in project with not very big amount of files (not to waste-load them) -->
    <python.test.mask>[Tt]est*.py</python.test.mask>
    
    <commons-cli.version>1.5.0</commons-cli.version>
    <commons-collections.version>3.2.2</commons-collections.version>
    <commons-configuration2.version>2.8.0</commons-configuration2.version>
    <commons-lang.version>2.6</commons-lang.version>
    <commons-lang3.version>3.12.0</commons-lang3.version>
    <commons-io.version>2.8.0</commons-io.version>
    <commons-logging.version>1.1.3</commons-logging.version>
    <commons-compress.version>1.21</commons-compress.version>
    <guava.version>27.0-jre</guava.version>
    <hadoop.version>3.3.4</hadoop.version>
    <hadoop-auth.version>3.3.4</hadoop-auth.version>
    <hadoop-shaded-guava.version>1.1.1</hadoop-shaded-guava.version>
    <slf4j.version>1.7.36</slf4j.version>
    <woodstox-core.version>5.4.0</woodstox-core.version>
    <stax2-api.version>4.2.1</stax2-api.version>
    <curator.version>4.2.0</curator.version>
  </properties>
  <dependencies>
    <dependency>
      <groupId>org.apache.zookeeper</groupId>
      <artifactId>zookeeper</artifactId>
      <exclusions>
        <exclusion>
          <groupId>log4j</groupId>
          <artifactId>log4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-log4j12</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>commons-cli</groupId>
      <artifactId>commons-cli</artifactId>
      <version>${commons-cli.version}</version>
    </dependency>
    <dependency>
      <groupId>commons-collections</groupId>
      <artifactId>commons-collections</artifactId>
      <version>${commons-collections.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-configuration2</artifactId>
      <version>${commons-configuration2.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>${commons-lang3.version}</version>
    </dependency>
    <dependency>
      <groupId>commons-lang</groupId>
      <artifactId>commons-lang</artifactId>
      <version>${commons-lang.version}</version>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>${commons-io.version}</version>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>${commons-logging.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-compress</artifactId>
      <version>${commons-compress.version}</version>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>${guava.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-common</artifactId>
      <version>${hadoop.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.sun.jersey</groupId>
          <artifactId>jersey-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.sun.jersey</groupId>
          <artifactId>jersey-servlet</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.sun.jersey</groupId>
          <artifactId>jersey-server</artifactId>
        </exclusion>
        <exclusion>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
        </exclusion>
        <exclusion>
            <groupId>org.mortbay.jetty</groupId>
            <artifactId>jetty-util</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.mortbay.jetty</groupId>
          <artifactId>jetty</artifactId>
        </exclusion>
        <exclusion>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
        </exclusion>
        <exclusion>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
        </exclusion>
        <exclusion>
          <groupId>log4j</groupId>
          <artifactId>log4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-log4j12</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-auth</artifactId>
      <version>${hadoop-auth.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop.thirdparty</groupId>
      <artifactId>hadoop-shaded-guava</artifactId>
      <version>${hadoop-shaded-guava.version}</version>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>${slf4j.version}</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.woodstox</groupId>
      <artifactId>woodstox-core</artifactId>
      <version>${woodstox-core.version}</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.woodstox</groupId>
      <artifactId>stax2-api</artifactId>
      <version>${stax2-api.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.zookeeper</groupId>
      <artifactId>zookeeper-jute</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.curator</groupId>
      <artifactId>curator-test</artifactId>
      <version>${curator.version}</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.curator</groupId>
      <artifactId>curator-framework</artifactId>
      <version>${curator.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.curator</groupId>
      <artifactId>curator-client</artifactId>
      <version>${curator.version}</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>log4j</groupId>
          <artifactId>log4j</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.ambari</groupId>
      <artifactId>ambari-utility</artifactId>
      <scope>test</scope>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
         <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <version>1.8</version>
        <executions>
          <execution>
            <id>parse-version</id>
            <phase>validate</phase>
            <goals>
              <goal>parse-version</goal>
            </goals>
          </execution>
          <execution>
            <id>regex-property</id>
            <goals>
              <goal>regex-property</goal>
            </goals>
            <configuration>
              <name>ambariVersion</name>
              <value>${project.version}</value>
              <regex>^([0-9]+)\.([0-9]+)\.([0-9]+)\.([0-9]+)(\.|-).*</regex>
              <replacement>$1.$2.$3.$4</replacement>
              <failIfNoMatch>false</failIfNoMatch>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.5</version>
        <configuration>
          <source>${jdk.version}</source>
          <target>${jdk.version}</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <skip>${skipSurefireTests}</skip>
          <!-- Each profile in the top-level pom.xml defines which test group categories to run. -->
          <groups>${testcase.groups}</groups>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <executions>
          <execution>
            <configuration>
              <executable>${executable.python}</executable>
              <workingDirectory>src/test/python</workingDirectory>
              <arguments>
                <argument>unitTests.py</argument>
                <argument>${python.test.mask}</argument>
              </arguments>
              <environmentVariables>
                <PYTHONPATH>${path.python.1}${pathsep}${env.PYTHONPATH}</PYTHONPATH>
              </environmentVariables>
              <skip>${skipPythonTests}</skip>
            </configuration>
            <id>python-test</id>
            <phase>test</phase>
            <goals>
              <goal>exec</goal>
            </goals>
          </execution>
          <execution>
            <configuration>
              <executable>${executable.python}</executable>
              <workingDirectory>${basedir}</workingDirectory>
              <arguments>
                <argument>${resource.keeper.script}</argument>
                <argument>${target.cache.dir}</argument>
              </arguments>
              <environmentVariables>
                <PYTHONPATH>target${dirsep}ambari-agent-${project.version}${pathsep}${env.PYTHONPATH}</PYTHONPATH>
              </environmentVariables>
            </configuration>
            <id>generate-hash-files</id>
            <phase>package</phase>
            <goals>
              <goal>exec</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>properties-maven-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>read-project-properties</goal>
            </goals>
            <configuration>
              <files>
                <file>${basedir}/src/main/package/dependencies.properties</file>
              </files>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>rpm-maven-plugin</artifactId>
        <executions>
          <execution>
            <!-- unbinds rpm creation from maven lifecycle -->
            <phase>none</phase>
            <goals>
              <goal>rpm</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <license>2012, Apache Software Foundation</license>
          <group>Development</group>
          <description>Maven Recipe: RPM Package.</description>
          <defineStatements>
            <defineStatement>__python 3</defineStatement>
          </defineStatements>
          <requires>
            <require>${rpm.dependency.list}</require>
          </requires>
          <postinstallScriptlet>
            <scriptFile>src/main/package/rpm/postinstall.sh</scriptFile>
            <fileEncoding>utf-8</fileEncoding>
          </postinstallScriptlet>
          <preinstallScriptlet>
            <scriptFile>src/main/package/rpm/preinstall.sh</scriptFile>
            <fileEncoding>utf-8</fileEncoding>
          </preinstallScriptlet>
          <preremoveScriptlet>
            <scriptFile>src/main/package/rpm/preremove.sh</scriptFile>
            <fileEncoding>utf-8</fileEncoding>
          </preremoveScriptlet>
          <posttransScriptlet>
            <scriptFile>src/main/package/rpm/posttrans_agent.sh</scriptFile>
            <fileEncoding>utf-8</fileEncoding>
          </posttransScriptlet>
          <postremoveScriptlet>
            <scriptFile>src/main/package/rpm/postremove.sh</scriptFile>
            <fileEncoding>utf-8</fileEncoding>
          </postremoveScriptlet>

          <needarch>x86_64</needarch>
          <autoRequires>false</autoRequires>
          <mappings>
            <mapping>
              <directory>/etc</directory>
              <username>root</username>
              <groupname>root</groupname>
              <directoryIncluded>false</directoryIncluded> <!-- avoid managing /etc/init.d -->
              <sources>
                <source>
                  <location>${project.build.directory}${dirsep}${project.artifactId}-${project.version}/etc</location>
                </source>
              </sources>
            </mapping>
           <mapping>
              <directory>/usr/lib/ambari-agent</directory>
              <filemode>755</filemode>
              <username>root</username>
              <groupname>root</groupname>
              <sources>
                <source>
                  <location>${project.build.directory}${dirsep}${project.artifactId}-${project.version}/usr/lib/ambari-agent</location>
                </source>
              </sources>
            </mapping>
            <mapping>
              <directory>/var/lib/ambari-agent</directory>
              <filemode>755</filemode>
              <username>root</username>
              <groupname>root</groupname>
              <sources>
                <source>
                  <location>${project.build.directory}${dirsep}${project.artifactId}-${project.version}/var/lib/ambari-agent</location>
                </source>
              </sources>
            </mapping>
            <mapping>
              <directory>/var/lib/ambari-agent</directory>
              <filemode>644</filemode>
              <username>root</username>
              <groupname>root</groupname>
              <sources>
                <source>
                  <location>${project.build.directory}${dirsep}${project.artifactId}-${project.version}/var/lib/ambari-agent</location>
                  <includes>
                    <include>/cred/lib/*.jar</include>
                    <include>/tools/*.jar</include>
                    <include>/cache/stacks/HDP/2.1.GlusterFS/services/STORM/package/files/wordCount.jar</include>
                    <include>/cache/stack-hooks/before-START/files/fast-hdfs-resource.jar</include>
                    <include>/cache/common-services/STORM/0.9.1/package/files/wordCount.jar</include>
                  </includes>
                </source>
              </sources>
            </mapping>
            <mapping>
              <directory>/var/log/ambari-agent</directory>
              <username>root</username>
              <groupname>root</groupname>
              <sources>
                <source>
                  <location>${project.build.directory}${dirsep}${project.artifactId}-${project.version}/var/log/ambari-agent</location>
                </source>
              </sources>
            </mapping>
            <mapping>
              <directory>/var/run/ambari-agent</directory>
              <username>root</username>
              <groupname>root</groupname>
              <sources>
                <source>
                  <location>${project.build.directory}${dirsep}${project.artifactId}-${project.version}/var/run/ambari-agent</location>
                </source>
              </sources>
            </mapping>
          </mappings>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.vafer</groupId>
        <artifactId>jdeb</artifactId>
        <executions>
          <execution>
            <phase>none</phase>
            <goals>
              <goal>jdeb</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <controlDir>${basedir}/src/main/package/deb/control</controlDir>
          <deb>${basedir}/target/${project.artifactId}_${package-version}-${package-release}.deb</deb>
          <skip>false</skip>
          <skipPOMs>false</skipPOMs>
          <dataSet>
            <data>
              <src>${project.build.directory}${dirsep}${project.artifactId}-${project.version}.tar.gz</src>
              <type>archive</type>
              <mapper>
                <type>perm</type>
                <user>root</user>
                <group>root</group>
              </mapper>
            </data>
          </dataSet>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <configuration>
          <createDependencyReducedPom>false</createDependencyReducedPom>
        </configuration>
        <version>3.5.1</version>
        <executions>
          <execution>
            <id>shade-zkmigrator</id>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <transformers>
                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                  <mainClass>org.apache.ambari.tools.zk.ZkMigrator</mainClass>
                </transformer>
              </transformers>
              <outputFile>${project.build.directory}${dirsep}zkmigrator.jar</outputFile>
              <minimizeJar>true</minimizeJar>
              <artifactSet>
                <includes>
                  <include>org.apache.zookeeper:zookeeper</include>
                  <include>org.apache.zookeeper:zookeeper-jute</include>
                  <include>commons-cli:commons-cli</include>
                  <include>org.slf4j:*</include>
                  <include>log4j:*</include>
                </includes>
              </artifactSet>
            </configuration>
          </execution>
          <execution>
            <id>shade-jce</id>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <minimizeJar>true</minimizeJar>
              <artifactSet>
                <includes>
                  <include>commons-cli:commons-cli</include>
                  <include>org.slf4j:*</include>
                  <include>log4j:*</include>
                </includes>
              </artifactSet>
              <transformers>
                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                  <mainClass>org.apache.ambari.tools.jce.JcePolicyInfo</mainClass>
                </transformer>
              </transformers>
              <outputFile>${project.build.directory}${dirsep}jcepolicyinfo.jar</outputFile>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <tarLongFileMode>gnu</tarLongFileMode>
          <descriptors>
            <descriptor>src/packages/tarball/all.xml</descriptor>
          </descriptors>
        </configuration>
        <executions>
          <execution>
            <id>make-assembly</id>
            <phase>${assemblyPhase}</phase>
            <goals>
              <goal>single</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <version>2.6</version>
        <executions>
          <execution>
            <id>copy-resources</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${target.cache.dir}</outputDirectory>
              <resources>
                <resource>
                  <directory>${ambari.server.module}/src/main/resources</directory>
                  <includes>
                    <include>custom_actions/**/*</include>
                    <include>host_scripts/**/*</include>
                  </includes>
                  <filtering>false</filtering>
                </resource>
              </resources>
            </configuration>
          </execution>
          <execution>
            <id>copy-common-resources</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${target.cache.dir}</outputDirectory>
              <resources>
                <resource>
                  <directory>${resourcesFolder}</directory>
                  <includes>
                    <include>common-services/**</include>
                    <include>stack-hooks/**</include>
                    <include>stacks/stack_advisor.py</include>
                    <include>stacks/${stack.distribution}/**/*</include>
                  </includes>
                  <filtering>false</filtering>
                </resource>
              </resources>
            </configuration>
          </execution>
           <execution>
            <id>copy-resources-filter</id>
            <phase>process-resources</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${basedir}/target/src</outputDirectory>
              <resources>
                <resource>
                  <directory>conf/unix</directory>
                  <includes>
                    <include>ambari-agent</include>
                  </includes>
                  <filtering>true</filtering>
                </resource>
                <resource>
                  <directory>${basedir}/../</directory>
                  <includes>
                    <include>version</include>
                  </includes>
                  <filtering>true</filtering>
                </resource>
              </resources>
            </configuration>
          </execution>
          <execution>
            <id>copy-repo-resources</id>
            <phase>${assemblyPhase}</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${basedir}/target/repo</outputDirectory>
              <resources>
                <resource>
                  <directory>${project.basedir}/../ambari-common/src/main/repo</directory>
                </resource>
                <resource>
                  <directory>${project.build.directory}</directory>
                  <includes>
                    <include>${project.artifactId}-${project.version}.tar.gz</include>
                  </includes>
                </resource>
                <resource>
                  <directory>${basedir}/src/main/package/deb/control</directory>
                </resource>
                <resource>
                  <directory>${basedir}/src/main/package</directory>
                  <includes>
                    <include>dependencies.properties</include>
                  </includes>
                </resource>
                <resource>
                  <directory>${project.basedir}/../ambari-common/src/main/python/ambari_commons</directory>
                  <includes>
                    <include>os_check.py</include>
                    <include>resources/os_family.json</include>
                  </includes>
                </resource>
              </resources>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.coderplus.maven.plugins</groupId>
        <artifactId>copy-rename-maven-plugin</artifactId>
        <version>1.0.1</version>
        <executions>
          <execution>
            <id>rename-file</id>
            <phase>${assemblyPhase}</phase>
            <goals>
              <goal>rename</goal>
            </goals>
            <configuration>
              <sourceFile>${basedir}/target/repo/${project.artifactId}-${project.version}.tar.gz</sourceFile>
              <destinationFile>${basedir}/target/repo/${project.artifactId}.tar.gz</destinationFile>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>conf/unix/logging.conf.sample</exclude>
            <exclude>conf/windows/logging.conf.sample</exclude>
            <exclude>src/test/python/tmp_hostcheck.result</exclude>
            <exclude>src/examples/*</exclude>
            <exclude>src/test/python/dummy*.txt</exclude>
            <exclude>src/test/python/ambari_agent/dummy_files/*</exclude>
            <exclude>src/test/python/ambari_agent/dummy*.txt</exclude>
            <exclude>src/main/python/ambari_agent/imports.txt</exclude>
            <exclude>src/main/python/ambari_agent/apscheduler/**</exclude>
            <exclude>**/*.erb</exclude>
            <exclude>**/*.json</exclude>
            <exclude>**/*.pydevproject</exclude>
            <exclude>src/main/package/choco/ambari-agent.nuspec</exclude>
          </excludes>
        </configuration>
        <executions>
          <execution>
            <phase>test</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>buildnumber-maven-plugin</artifactId>
          <version>${buildnumber-maven-plugin-version}</version>
          <configuration>
              <urlScm>scm:git:https://gitbox.apache.org/repos/asf/ambari.git</urlScm>
          </configuration>
          <executions>
              <execution>
                  <phase>validate</phase>
                  <goals>
                      <goal>create</goal>
                  </goals>
              </execution>
          </executions>
      </plugin>
    </plugins>
    <extensions>
      <extension>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-ssh-external</artifactId>
      </extension>
    </extensions>
  </build>
  <profiles>
    <profile>
     <id>windows</id>
     <activation>
      <os>
        <family>win</family>
      </os>
     </activation>
     <properties>
      <envClassifier>win</envClassifier>
      <dirsep>\</dirsep>
      <pathsep>;</pathsep>
      <stack.distribution>HDPWIN</stack.distribution>
      <executable.python>python3</executable.python>
      <executable.shell>cmd</executable.shell>
      <fileextension.shell>cmd</fileextension.shell>
      <fileextension.dot.shell-default>.cmd</fileextension.dot.shell-default>
      <path.python.1>${project.basedir}\..\ambari-common\src\main\python;${project.basedir}\..\ambari-agent\src\main\python;${project.basedir}\..\ambari-common\src\main\python\ambari_jinja2;${project.basedir}\..\ambari-common\src\main\python\ambari_commons;${project.basedir}\..\ambari-common\src\test\python;${project.basedir}\src\main\python;${project.basedir}\src\main\python\ambari_agent;${project.basedir}\src\main\python\resource_management;${project.basedir}\src\test\python;${project.basedir}\src\test\python\ambari_agent;${project.basedir}\src\test\python\resource_management;${project.basedir}\..\ambari-server\src\test\python;${project.basedir}\..\ambari-server\src\main\resources\common-services\HDFS\*******.0\package\files</path.python.1>
     </properties>
    </profile>
    <profile>
     <id>linux</id>
     <activation>
      <os>
        <family>unix</family>
      </os>
     </activation>
     <properties>
      <envClassifier>linux</envClassifier>
      <dirsep>/</dirsep>
      <pathsep>:</pathsep>
      <stack.distribution>HDP</stack.distribution>
      <executable.python>${project.basedir}/../ambari-common/src/main/unix/ambari-python-wrap</executable.python>
      <executable.shell>sh</executable.shell>
      <fileextension.shell>sh</fileextension.shell>
      <fileextension.dot.shell-default></fileextension.dot.shell-default>
      <path.python.1>${project.basedir}/../ambari-common/src/main/python:${project.basedir}/../ambari-agent/src/main/python:${project.basedir}/../ambari-common/src/main/python/ambari_jinja2:${project.basedir}/../ambari-agent/src/main/python:${project.basedir}/../ambari-common/src/main/python/ambari_commons:${project.basedir}/../ambari-common/src/test/python:${project.basedir}/src/main/python:${project.basedir}/src/main/python/ambari_agent:${project.basedir}/src/main/python/resource_management:${project.basedir}/src/test/python:${project.basedir}/src/test/python/ambari_agent:${project.basedir}/src/test/python/resource_management:${project.basedir}/../ambari-server/src/test/python:${project.basedir}/../ambari-server/src/main/resources/common-services/HDFS/*******.0/package/files</path.python.1>
     </properties>
    </profile>
    <profile>
      <id>windows-distro</id>
      <activation>
        <os>
          <family>Windows</family>
        </os>
      </activation>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-assembly-plugin</artifactId>
            <configuration>
              <tarLongFileMode>gnu</tarLongFileMode>
              <descriptors>
                <descriptor>src/packages/tarball/all.xml</descriptor>
                <descriptor>src/packages/windows.xml</descriptor>
              </descriptors>
            </configuration>
            <executions>
              <execution>
                <id>build-windows-zip</id>
                <phase>package</phase>
                <goals>
                  <goal>single</goal>
                </goals>
              </execution>
              <execution>
                <id>build-choco-dir</id>
                <phase>package</phase>
                <goals>
                  <goal>single</goal>
                </goals>
                <configuration>
                  <attach>false</attach>
                  <appendAssemblyId>true</appendAssemblyId>
                  <tarLongFileMode>gnu</tarLongFileMode>
                  <descriptors>
                    <descriptor>src/packages/windows-choco.xml</descriptor>
                  </descriptors>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <!-- choco package creation -->
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>exec-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>build-choco-package</id>
                <phase>package</phase>
                <goals>
                  <goal>exec</goal>
                </goals>
                <configuration>
                  <executable>choco.exe</executable>
                  <arguments>
                    <argument>pack</argument>
                    <argument>--version=${project.version}</argument>
                    <argument>${basedir}/target/ambari-agent-${project.version}-choco/ambari-agent.nuspec</argument>
                  </arguments>
                  <workingDirectory>target/ambari-agent-${project.version}-choco</workingDirectory>
                </configuration>
              </execution>
            </executions>
          </plugin>
            <!-- end choco package creation -->
        </plugins>
      </build>
    </profile>
    <profile>
      <id>pluggable-stack-definition</id>
      <activation>
        <activeByDefault>false</activeByDefault>
        <property>
          <name>pluggableStackDefinitionConfig</name>
        </property>
      </activation>
      <properties>
        <resourceManagementSrcLocation>target/pluggable-stack-definition/python/resource_management</resourceManagementSrcLocation>
        <resourcesFolder>target/pluggable-stack-definition</resourcesFolder>
        <resourcesSrcLocation>${project.basedir}/../ambari-server/src/main/resources</resourcesSrcLocation>
        <pluggableStackDefinitionScriptLocation>${project.basedir}/../ambari-common/src/main/python/pluggable_stack_definition/GenerateStackDefinition.py</pluggableStackDefinitionScriptLocation>
        <pluggableStackDefinitionConfig>${project.basedir}/../ambari-common/src/main/python/pluggable_stack_definition/configs/${stack.distribution}.json</pluggableStackDefinitionConfig>
        <pluggableStackDefinitionOutput>target/pluggable-stack-definition</pluggableStackDefinitionOutput>
        <customActionsLocation>${pluggableStackDefinitionOutput}/custom_actions</customActionsLocation>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>exec-maven-plugin</artifactId>
            <executions>
              <execution>
                <configuration>
                  <executable>${executable.python}</executable>
                  <arguments>
                    <argument>${pluggableStackDefinitionScriptLocation}</argument>
                    <argument>-c</argument>
                    <argument>${pluggableStackDefinitionConfig}</argument>
                    <argument>-r</argument>
                    <argument>${resourcesSrcLocation}</argument>
                    <argument>-o</argument>
                    <argument>${pluggableStackDefinitionOutput}</argument>
                  </arguments>
                </configuration>
                <id>pluggable-stack-definition-generate</id>
                <phase>generate-resources</phase>
                <goals>
                  <goal>exec</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
