<!--
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
-->
<div class="clearfix">
  <ol class="breadcrumb pull-left">
    <li><a href="#/remoteClusters">{{'common.remoteClusters' | translate}}</a></li>
    <li class="active">{{cluster.cluster_name}}</li>
  </ol>
  <div class="pull-right top-margin-4">
    <div>
      <button class="btn deleteuser-btn btn-danger" ng-click="deleteCluster()">{{'common.deregisterCluster' | translate}}</button>
    </div>
  </div>
</div>
<hr>

<form class="form-horizontal create-user-form" role="form" novalidate name="form" autocomplete="off">

  <div class="form-group" ng-class="{'has-error' : form.user_name.$error.required && form.submitted}">
    <label for="clustername" class="col-sm-2 control-label">{{'views.clusterName' | translate}}*</label>
    <div class="col-sm-10">
      <input type="text" id="clustername" class="form-control" name="cluster_name" ng-pattern="nameValidationPattern" placeholder="{{'views.clusterName' | translate}}" ng-model="cluster.cluster_name" required autocomplete="off">
      <div class="alert alert-danger top-margin" ng-show="form.cluster_name.$error.required && form.submitted"> {{'common.alerts.fieldIsRequired' | translate}}</div>
      <div class="alert alert-danger top-margin" ng-show='form.cluster_name.$error.pattern && form.submitted'>{{'views.alerts.noSpecialCharsOrSpaces' | translate}}</div>
    </div>
  </div>

  <div class="form-group" ng-class="{'has-error' : form.user_name.$error.required && form.submitted}">
    <label for="clusterurl" class="col-sm-2 control-label">{{'users.ambariClusterURL' | translate}}*</label>
    <div class="col-sm-10">
      <input type="text" id="clusterurl" class="form-control" ng-pattern="urlValidationPattern" name="cluster_url" placeholder="{{'users.ambariClusterURL' | translate}}" ng-model="cluster.cluster_url" required autocomplete="off">
      <div class="alert alert-danger top-margin" ng-show="form.cluster_url.$error.required && form.submitted"> {{'common.alerts.fieldIsRequired' | translate}}</div>
      <div class="alert alert-danger top-margin" ng-show="form.cluster_url.$error.pattern && form.submitted"> {{'views.alerts.invalidUrl' | translate}}</div>
    </div>
  </div>

  <div class="form-group" ng-class="{'has-error' : form.user_name.$error.required && form.submitted}" style="display: none" >
    <label for="clusteruser" class="col-sm-2 control-label"> {{'users.roles.clusterUser' | translate}}*</label>
    <div class="col-sm-10">
      <input type="text" id="clusteruser" class="form-control" name="cluster_user" placeholder="{{'users.roles.clusterUser' | translate}}" ng-model="cluster.cluster_user" required autocomplete="off">
      <div class="alert alert-danger top-margin" ng-show="form.cluster_user.$error.required && form.submitted"> {{'common.alerts.fieldIsRequired' | translate}}</div>
    </div>
  </div>

  <div class="form-group">
    <label for="" class="col-sm-2 control-label"></label>
    <div class="col-sm-10">
      <div>
        <a href ng-click="openChangePwdDialog()" class="btn btn-default changepassword">{{'users.updateCredentials' | translate}}</a>
      </div>
    </div>
  </div>


  <div class="form-group">
    <div class="col-sm-offset-2 col-sm-10">
      <button class="btn btn-primary pull-right left-margin saveremotecluster" ng-disabled="form.$pristine" ng-click="editRemoteCluster()">{{'common.controls.save' | translate}}</button>
      <a class="btn btn-default pull-right cancel" href ng-click="cancel()">{{'common.controls.cancel' | translate}}</a>
    </div>
  </div>

</form>
