<!--
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
-->

<div id="user-management">
  <ul class="nav nav-tabs">
    <li ng-class="{active: activeTab === 'USERS'}">
      <a href="#/userManagement?tab=users" >{{'common.users' | translate}}</a>
    </li>
    <li ng-class="{active: activeTab === 'GROUPS'}">
      <a href="#/userManagement?tab=groups" >{{'common.groups' | translate}}</a>
    </li>
  </ul>
  <div>
    <div class="users" ng-if="activeTab === 'USERS'">
      <div ng-include="'views/userManagement/usersList.html'" ng-controller="UsersListCtrl"></div>
    </div>
    <div class="groups" ng-if="activeTab === 'GROUPS'">
      <div ng-include="'views/userManagement/groupsList.html'" ng-controller="GroupsListCtrl"></div>
    </div>
  </div>
</div>
