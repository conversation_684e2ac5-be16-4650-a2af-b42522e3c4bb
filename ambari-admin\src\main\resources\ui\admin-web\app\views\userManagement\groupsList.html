<!--
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
-->
<div class="groups-pane">
  <div class="clearfix">
    <div class="pull-right">
      <button class="btn btn-default creategroup-btn pull-right" ng-click="createGroup()">
        {{'groups.createLocal' | translate}}
      </button>
    </div>
    <div class="search-box-button pull-right">
      <button class="btn btn-default" ng-click="toggleSearchBox()">
        <i class="fa fa-filter" aria-hidden="true"></i>
      </button>
      <div class="popup-arrow-up hide"></div>
    </div>
  </div>

  <div class="search-box-row hide">
    <combo-search suggestions="filters" filter-change="filterGroups" placeholder="Search"></combo-search>
  </div>

  <table class="table table-striped table-hover col-sm-12">
    <thead>
      <tr>
        <th class="col-sm-6">
          <span>{{'groups.name' | translate}}</span>
        </th>
        <th class="col-sm-2">
          <span>{{'common.type' | translate}}</span>
        </th>
        <th class="col-sm-2">
          <span>{{'groups.members' | translate}}</span>
        </th>
        <th class="col-sm-2">
          <span>{{'common.actions' | translate}}</span>
        </th>
      </tr>
    </thead>
    <tbody>
      <tr ng-repeat="group in groups | filter : { isShowed: true }">
        <td class="col-sm-8">
          <span>{{group.group_name}}</span>
        </td>
        <td class="col-sm-2">{{group.groupTypeName | translate}}</td>
        <td class="col-sm-2">{{'groups.membersPlural' | translate:{n: group.members && group.members.length || 0} }}</td>
        <td class="entity-actions">
          <link-to route="userManagement.editGroup" class="link-to-group" id="{{group.group_name}}">
            <i class="fa fa-pencil"></i>
          </link-to>
          <a href ng-click="deleteGroup(group)">
            <i class="fa fa-trash-o"></i>
          </a>
        </td>
      </tr>
    </tbody>
  </table>
  <div ng-if="isLoading" class="spinner-container">
    <i class="fa fa-2x fa-spinner fa-spin" aria-hidden="true"></i>
  </div>
  <div class="alert empty-table-alert col-sm-12" ng-show="!tableInfo.showed && !isLoading">
    {{'common.alerts.nothingToDisplay' | translate:{term: constants.groups} }}
  </div>
  <div class="col-sm-12 table-bar" ng-show="tableInfo.total > minRowsToShowPagination">
    <div class="pull-left filtered-info">
      <span>{{'common.filterInfo' | translate:{showed: tableInfo.showed, total: tableInfo.filtered, term: constants.groups} }}</span>
    </div>
    <div class="pull-right left-margin">
      <pagination class="paginator" total-items="tableInfo.filtered" max-size="pagination.maxVisiblePages" items-per-page="pagination.itemsPerPage" ng-model="pagination.currentPage" ng-change="pageChanged()"></pagination>
    </div>
    <div class="pull-right">
      <select class="form-control" ng-model="pagination.itemsPerPage" ng-change="resetPagination()" ng-options="currOption for currOption in [10, 25, 50, 100]"></select>
    </div>
  </div>
</div>