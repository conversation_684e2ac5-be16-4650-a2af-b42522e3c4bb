<!--
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
-->

<div class="users-pane">
  <div class="clearfix">
    <div class="pull-right">
      <button class="btn btn-default createuser-btn pull-right" ng-click="createUser();">
        {{'users.create' | translate}}
      </button>
    </div>
    <div class="search-box-button pull-right">
      <button class="btn btn-default" ng-click="toggleSearchBox()">
        <i class="fa fa-filter" aria-hidden="true"></i>
      </button>
      <div class="popup-arrow-up hide"></div>
    </div>
  </div>

  <div class="search-box-row hide">
    <combo-search suggestions="filters" filter-change="filterUsers" placeholder="Search"></combo-search>
  </div>

  <table class="table table-striped table-hover">
    <thead>
      <tr>
        <th>
          <span>{{'users.username' | translate}}</span>
        </th>
        <th>
          <span>{{'clusters.role' | translate}}</span>
        </th>
        <th>
          <span>{{'users.status' | translate}}</span>
        </th>
        <th>
          <span>{{'common.type' | translate}}</span>
        </th>
        <th>
          <span>{{'common.group' | translate}}</span>
        </th>
        <th class="entity-actions">
          <span>{{'common.actions' | translate}}</span>
        </th>
      </tr>
    </thead>
    <tbody>
      <tr ng-repeat="user in users | filter : { isShowed: true }">
        <td>
          <span>{{user.Users.user_name}}</span>
        </td>
        <td>
          <span>{{user.Users.roles[0].permission_label}}</span>
        </td>
        <td>
          <span>
            {{(user.Users.active ? 'users.active' : 'users.inactive') | translate}}
          </span>
        </td>
        <td><span>{{user.Users.userTypeName}}</span></td>
        <td><span>{{user.Users.groups.length ? user.Users.groups.join(' ') : '-'}}</span></td>
        <td class="entity-actions">
          <a href="#/users/{{user.Users.encodedName}}/edit">
            <i class="fa fa-pencil"></i>
          </a>
          <a href ng-click="deleteUser(user.Users)" ng-disabled="!user.Users.isDeletable">
            <i class="fa fa-trash-o"></i>
          </a>
        </td>
      </tr>
    </tbody>
  </table>
  <div ng-if="isLoading" class="spinner-container">
    <i class="fa fa-2x fa-spinner fa-spin" aria-hidden="true"></i>
  </div>
  <div class="alert empty-table-alert col-sm-12" ng-show="!tableInfo.showed && !isLoading">
    {{'common.alerts.nothingToDisplay' | translate:{term: constants.users} }}
  </div>
  <div class="col-sm-12 table-bar" ng-show="users.length > minRowsToShowPagination">
    <div class="pull-left filtered-info">
      <span>{{'common.filterInfo' | translate:{showed: tableInfo.showed, total: tableInfo.total, term: constants.users} }}</span>
    </div>
    <div class="pull-right left-margin">
      <pagination class="paginator" total-items="tableInfo.filtered" max-size="pagination.maxVisiblePages" items-per-page="pagination.itemsPerPage" ng-model="pagination.currentPage" ng-change="pageChanged()"></pagination>
    </div>
    <div class="pull-right">
      <select class="form-control" ng-model="pagination.itemsPerPage" ng-change="resetPagination()" ng-options="currOption for currOption in [10, 25, 50, 100]"></select>
    </div>
  </div>
</div>
