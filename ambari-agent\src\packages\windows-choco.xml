<?xml version="1.0"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<assembly>
  <id>choco</id>
  <formats>
    <format>dir</format>
  </formats>
  <includeBaseDirectory>false</includeBaseDirectory>
  <files>
    <file>
      <source>${project.build.directory}/${artifact.artifactId}-${artifact.version}-windows-dist.zip</source>
      <outputDirectory>content</outputDirectory>
    </file>
    <file>
      <source>${basedir}/src/main/package/choco/ambari-agent.nuspec</source>
    </file>
    <file>
      <source>${basedir}/src/main/package/choco/chocolateyinstall.ps1</source>
      <outputDirectory>tools</outputDirectory>
    </file>
    <file>
      <source>${basedir}/src/main/package/choco/chocolateyuninstall.ps1</source>
      <outputDirectory>tools</outputDirectory>
    </file>
  </files>
  <fileSets>
    <fileSet>
      <directory>${basedir}/../ambari-common/src/main/windows</directory>
      <outputDirectory>modules</outputDirectory>
      <includes>
        <include>*.psm1</include>
      </includes>
    </fileSet>
  </fileSets>
</assembly>
