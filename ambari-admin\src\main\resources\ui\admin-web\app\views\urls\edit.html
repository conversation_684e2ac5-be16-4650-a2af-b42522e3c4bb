<!--
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
-->
<ol class="breadcrumb">
  <li class="active">{{'urls.edit' | translate}} {{url.url_name}}</li>
  <div class="pull-right top-margin-4">
    <button class="btn deleteuser-btn btn-danger" ng-click="deleteUrl()">{{'views.urlDelete' | translate}}</button>
  </div>
</ol>
<hr>


<form  class="form-horizontal create-user-form" role="form" novalidate name="url_form" autocomplete="off">
  <div class="form-group" ng-class="{'has-error' : url_form.url_name.$error.required  && url_form.submitted}">
    <label for="urlname" class="col-sm-2 control-label">{{'common.name' | translate}}</label>
    <div class="col-sm-10">
      <input disabled type="text" id="urlname" class="form-control urlname-input" name="url_name" placeholder="{{'common.name' | translate}}" ng-model="url.url_name" required autocomplete="off">
      <div class="alert alert-danger top-margin" ng-show="url_form.url_name.$error.required  && url_form.submitted">{{'common.alerts.fieldIsRequired' | translate}}</div>
    </div>
  </div>


  <div class="form-group" ng-class="{'has-error' : url_form.url_view_name.$error.required  && url_form.submitted}">
    <label for="urlselect" class="col-sm-2 control-label">{{'urls.view' | translate}}</label>
    <div class="col-sm-10">
      <input class="form-control" disabled id="urlselect" name="url_view_name" ng-model="nameVersion" required>
      <div class="alert alert-danger top-margin" ng-show="url_form.url_view_name.$error.required  && url_form.submitted">{{'common.alerts.fieldIsRequired' | translate}}</div>
    </div>
  </div>

  <div class="form-group" ng-class="{'has-error' : url_form.url_view_instance_name.$error.required  && url_form.submitted}">
    <label for="urlinstanceselect" class="col-sm-2 control-label">{{'urls.viewInstance' | translate}}</label>
    <div class="col-sm-10">
      <input class="form-control" disabled id="urlinstanceselect" name="url_view_instance_name"  ng-model="url.view_instance_name" required>
      <div class="alert alert-danger top-margin" ng-show="url_form.url_view_instance_name.$error.required  && url_form.submitted">{{'common.alerts.fieldIsRequired' | translate}}</div>
    </div>
  </div>



  <div class="form-group" ng-class="{'has-error' : url_form.url_view_suffix.$error.required  && url_form.submitted}">
    <label for="urlsuffixin" class="col-sm-2 control-label">{{'views.shortUrl' | translate}}</label>
    <div class="col-sm-10">
      <div class="input-group">
      <span id="basic-addon1" class="input-group-addon">/main/view/{{url.view_instance_common_name}}/</span><input aria-describedby="basic-addon1" type="text" class="form-control" id="urlsuffixin" name="url_view_suffix" placeholder="{{'views.shortUrl' | translate}}" ng-model="url.url_suffix" ng-pattern="/^[a-z0-9-_]+$/" ng-minlength="2" ng-maxlength="25" required autocomplete="off">
        </div>
      <div class="alert alert-danger top-margin" ng-show="url_form.url_view_suffix.$error.required   && url_form.submitted">{{'common.alerts.fieldIsRequired' | translate}}</div>
      <div class="alert alert-danger top-margin" ng-show="url_form.url_view_suffix.$error.minlength   && url_form.submitted">{{'common.alerts.minimumTwoChars' | translate}}</div>
      <div class="alert alert-danger top-margin" ng-show="url_form.url_view_suffix.$error.maxlength   && url_form.submitted">{{'common.alerts.maxTwentyFiveChars' | translate}}</div>
      <div class="alert alert-danger top-margin" ng-show="url_form.url_view_suffix.$error.pattern   && url_form.submitted">{{'common.alerts.onlyText' | translate}}</div>

    </div>
  </div>

  <div class="form-group">
    <div class="col-sm-offset-2 col-sm-10">
      <button ng-disabled="stepTwoNotCompleted" class="btn btn-primary pull-right left-margin saveuser" ng-click="updateUrl()">{{'common.controls.save' | translate}}</button>
    </div>
  </div>

</form>
