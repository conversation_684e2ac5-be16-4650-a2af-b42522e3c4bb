#!/usr/bin/env bash
# chkconfig: 345 95 20
# description: ambari-agent daemon
# processname: ambari-agent

# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

### BEGIN INIT INFO
# Provides:          ambari-agent
# Required-Start:    $local_fs $remote_fs $network
# Required-Start:
# Required-Stop:
# Default-Start:     2 3 4 5
# Default-Stop:      0 6
### END INIT INFO

run_as_user=`cat /etc/ambari-agent/conf/ambari-agent.ini | grep run_as_user | tr -d ' ' | grep -v '^;\|^#' | awk -F '=' '{ print $2}'`

if [ "$EUID" != `id -u $run_as_user` ] ; then
  command_prefx="su - $run_as_user -c"
else
  command_prefx="bash -c"
fi

eval "$command_prefx '/var/lib/ambari-agent/bin/ambari-agent $@'"

exit $?
