<!--
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
-->
<ol class="breadcrumb">
  <li><a href="#/remoteClusters">{{'common.remoteClusters' | translate}}</a></li>
  <li class="active">{{'common.register' | translate}}</li>
</ol>
<hr>
<form class="form-horizontal create-user-form" role="form" novalidate name="form" autocomplete="off">

  <div class="form-group" ng-class="{'has-error' : form.user_name.$error.required && form.submitted}">
    <label for="clustername" class="col-sm-2 control-label">{{'views.clusterName' | translate}}* </label>
    <div class="col-sm-10">
      <input type="text" id="clustername" class="form-control" ng-pattern="nameValidationPattern" name="cluster_name" placeholder="{{'remoteClusters.ambariClusterName' | translate}}" ng-model="cluster.cluster_name" required autocomplete="off">
      <div class="alert alert-danger top-margin" ng-show="form.cluster_name.$error.required && form.submitted"> {{'common.alerts.fieldIsRequired' | translate}}</div>
      <div class="alert alert-danger top-margin" ng-show='form.cluster_name.$error.pattern && form.submitted'>{{'views.alerts.noSpecialCharsOrSpaces' | translate}}</div>
    </div>
  </div>

  <div class="form-group" ng-class="{'has-error' : form.user_name.$error.required && form.submitted}">
    <label for="clusterurl" class="col-sm-2 control-label">{{'users.ambariClusterURL' | translate}}*</label>
    <div class="col-sm-10">
      <input type="text" id="clusterurl" class="form-control" ng-pattern="urlValidationPattern" name="cluster_url" placeholder="{{'remoteClusters.clusterURLPlaceholder' | translate}}" ng-model="cluster.cluster_url" required autocomplete="off">
      <div class="alert alert-danger top-margin" ng-show="form.cluster_url.$error.required && form.submitted"> {{'common.alerts.fieldIsRequired' | translate}}</div>
      <div class="alert alert-danger top-margin" ng-show="form.cluster_url.$error.pattern && form.submitted"> {{'views.alerts.invalidUrl' | translate}}</div>
    </div>
  </div>

  <div class="form-group" ng-class="{'has-error' : form.user_name.$error.required && form.submitted}">
    <label for="clusteruser" class="col-sm-2 control-label"> {{'users.roles.clusterUser' | translate}}*</label>
    <div class="col-sm-10">
      <input type="text" id="clusteruser" class="form-control" name="cluster_user" placeholder="{{'users.roles.clusterUser' | translate}}" ng-model="cluster.cluster_user" required autocomplete="off">
      <div class="alert alert-danger top-margin" ng-show="form.cluster_user.$error.required && form.submitted"> {{'common.alerts.fieldIsRequired' | translate}}</div>
    </div>
  </div>

  <div class="form-group" ng-class="{'has-error' : (form.password.$error.required && form.submitted) || form.confirmPassword.$error.passwordVerify}">
    <label for="password" class="col-sm-2 control-label">{{'users.password' | translate}}*</label>
    <div class="col-sm-10">
      <input type="password" id="password" class="form-control bottom-margin userpassword" name="password" placeholder="{{'users.password' | translate}}" required ng-model="cluster.cluster_password" autocomplete="off">
      <div class="alert alert-danger" ng-show='form.confirmPassword.$error.passwordVerify'>{{'users.alerts.wrongPassword' | translate}}</div>
      <div class="alert alert-danger" ng-show='form.password.$error.required && form.submitted'>{{'common.alerts.passwordRequired' | translate}}</div>
    </div>
  </div>

  <div class="form-group">
    <div class="col-sm-offset-2 col-sm-10">
      <button class="btn btn-primary pull-right left-margin saveremotecluster" ng-click="registerRemoteCluster()">{{'common.controls.save' | translate}}</button>
      <a class="btn btn-default pull-right cancel" href ng-click="cancel()">  {{'common.controls.cancel' | translate}}</a>
    </div>
  </div>

</form>
