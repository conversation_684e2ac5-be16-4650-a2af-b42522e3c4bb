<!--
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
-->

<div id="stack-versions">
  <div class="clearfix">
    <div class="pull-right">
      <a href="#/stackVersions/create" class="btn btn-default">
        {{'versions.register.title' | translate}}
      </a>
    </div>
    <div class="search-box-button pull-right">
      <button class="btn btn-default" ng-click="toggleSearchBox()">
        <i class="fa fa-filter" aria-hidden="true"></i>
      </button>
      <div class="popup-arrow-up hide"></div>
    </div>
  </div>

  <div class="search-box-row hide">
    <combo-search suggestions="filters" filter-change="filterRepos" placeholder="Search"></combo-search>
  </div>

  <table class="table table-striped table-hover">
    <thead>
    <tr>
      <th class="col-small">
        <label>{{'common.stack' | translate}}</label>
      </th>
      <th class="col-medium text-search-container">
        <label>{{'common.name' | translate}}</label>
      </th>
      <th class="col-medium text-search-container">
        <label>{{'common.type' | translate}}</label>
      </th>
      <th class="col-medium text-search-container">
        <label>{{'common.version' | translate}}</label>
      </th>
      <th class="col-small">
        <label>{{'common.cluster' | translate}}</label>
      </th>
      <th class="col-small"></th>
      <th class="col-small text-center vertical-top">{{'common.hidden' | translate}}</th>
    </tr>
    </thead>
    <tbody>
    <tr ng-repeat="repo in repos | filter : { isShowed: true }">
      <td class="col-small">
        <span>{{repo.stack_name}}-{{repo.stack_version}}</span>
      </td>
      <td class="col-medium">
        <a href="#/stackVersions/{{repo.stack_name}}/{{repo.repository_version}}/edit">
          {{repo.display_name}}
        </a>
      </td>
      <td>
        <span>{{repo.type}}</span>
      </td>
      <td class="col-medium">
        <span>{{repo.repository_version}}</span>
      </td>
      <td class="col-small ellipsis-overflow">
        <a href="{{fromSiteRoot('/#!/main/admin/stack/versions')}}" ng-show="repo.cluster">
          {{repo.cluster}}
        </a>
        <span ng-show="!repo.cluster">
          {{'common.none' | translate}}
        </span>
      </td>
      <td class="verison-label-row" ng-class="{'button-padding': !repo.cluster}">
        <div ng-show="repo.status == 'CURRENT'">
          <span class="label {{'status-' + repo.status}}">{{'versions.current' | translate}}:&nbsp;{{repo.currentHosts}}/{{repo.totalHosts}}</span>
        </div>
        <div ng-show="repo.status == 'INSTALLED'">
          <span class="label {{'status-' + repo.status}}">{{'versions.installed' | translate}}:&nbsp;{{repo.installedHosts}}/{{repo.totalHosts}}</span>
        </div>
        <div ng-show="!repo.cluster">
          <div class="btn-group" ng-class="{open: viewsdropdown.isopen}" ng-mouseover="viewsdropdown.isopen=true" ng-mouseout="viewsdropdown.isopen=false" ng-init="viewsdropdown.isopen=false">
            <a class="btn dropdown-toggle">
              <span>{{'versions.installOn' | translate}}</span>
            </a>
            <ul class="dropdown-menu">
              <li ng-repeat="cluster in dropDownClusters">
                <a href="javascript:void(null)" ng-click="goToCluster()">
                  <span>{{cluster.Clusters.cluster_name}}</span>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </td>
      <td class="text-center">
        <div class="checkbox">
          <input ng-attr-id="{{ 'hidden-' + repo.id }}" type="checkbox" class="form-control" data-ng-model="repo.hidden" data-ng-change="toggleVisibility(repo)" data-ng-disabled="!isHideCheckBoxEnabled(repo)"/>
          <label ng-attr-for="{{ 'hidden-' + repo.id }}"><span></span></label>
        </div>
      </td>
    </tr>
    </tbody>
  </table>
  <div ng-if="isLoading" class="spinner-container">
    <i class="fa fa-2x fa-spinner fa-spin" aria-hidden="true"></i>
  </div>
  <div class="alert empty-table-alert col-sm-12" ng-show="!tableInfo.showed && !isLoading">
    {{'common.alerts.nothingToDisplay' | translate:{term: getConstant("common.version")} }}
  </div>
  <div class="col-sm-12 table-bar" ng-show="tableInfo.total >= minInstanceForPagination">
    <div class="pull-left filtered-info">
      <span>{{'common.filterInfo' | translate:{showed: tableInfo.showed, total: tableInfo.total, term: getConstant("common.versions")} }}</span>
    </div>
    <div class="pull-right left-margin">
      <pagination class="paginator"
                  total-items="tableInfo.total"
                  max-size="pagination.maxVisiblePages"
                  items-per-page="pagination.itemsPerPage"
                  ng-model="pagination.currentPage"
                  ng-change="pageChanged()"
        ></pagination>
    </div>
    <div class="pull-right">
      <select class="form-control"
              ng-model="pagination.itemsPerPage"
              ng-options="item for item in [10, 25, 50, 100]"
              ng-change="resetPagination()"
        ></select>
    </div>
  </div>
</div>
