<!--
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
-->
<div class="modal-header">
  <h3 class="modal-title">{{'users.updateCredentials' | translate}}</h3>
</div>
<div class="modal-body">
  <form class="form-horizontal" novalidate name="form.passwordChangeForm" role="form" >

    <div class="form-group" ng-class="{'has-error' : (form.passwordChangeForm.currentPassword.$error.required && form.passwordChangeForm.submitted)}">
      <label for="" class="col-sm-4 control-label" >{{'users.roles.clusterUser' | translate}}</label>
      <div class="col-sm-8">
        <input type="text" name="currentUserName" class="form-control bottom-margin" placeholder="Cluster User" required ng-model="passwordData.currentUserName" autocomplete="off">
        <div class="alert alert-danger no-margin-bottom" ng-show='form.passwordChangeForm.currentUserName.$error.required && form.passwordChangeForm.submitted'>{{'users.alerts.usernameRequired' | translate}}</div>
      </div>
    </div>

    <div class="form-group no-margin-bottom" ng-class="{'has-error' : (form.passwordChangeForm.password.$error.required && form.passwordChangeForm.submitted) || form.passwordChangeForm.confirmPassword.$error.passwordVerify}">
      <label for="" class="col-sm-4 control-label">{{'users.password' | translate}}</label>
      <div class="col-sm-8">
        <input type="password" class="form-control bottom-margin" name="password" placeholder="Password" required ng-model="passwordData.password" autocomplete="off">
        <div class="alert alert-danger no-margin-bottom" ng-show='form.passwordChangeForm.password.$error.required && form.passwordChangeForm.submitted'>{{'users.alerts.passwordRequired' | translate}}</div>
        <div class="alert alert-danger no-margin-bottom" ng-show='form.passwordChangeForm.confirmPassword.$error.passwordVerify'>{{'users.alerts.wrongPassword' | translate}}</div>
      </div>

    </div>
  </form>
</div>
<div class="modal-footer">
  <button class="btn btn-default" ng-click="cancel()">{{'common.controls.cancel' | translate}}</button>
  <button class="btn btn-primary" ng-click="ok()">{{'common.controls.update' | translate}}</button>
</div>