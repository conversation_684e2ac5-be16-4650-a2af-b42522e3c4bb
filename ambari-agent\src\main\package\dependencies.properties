# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information rega4rding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Please make sure the format of the file is one of the following
# rpm.dependency.list=...
# rpm.dependency.list.[os_family_name]=...
# rpm.dependency.list.[os_family_name][starting_from_version]=...
# deb.dependency.list=...
# deb.dependency.list.[os_family_name]=...
# deb.dependency.list.[os_family_name][starting_from_version]=...
# 
# Examples:
# rpm.dependency.list.suse=openssl,\nRequires: rpm-python # for all suse family os
# rpm.dependency.list.suse11=curl # for all suse family os higher or equal to 11.
#
# Such a format is respected by install_ambari_tarball.py by default, 
# however should be encouraged manually in pom.xml.

rpm.dependency.list=openssl,\nRequires: python3-rpm,\nRequires: zlib,\nRequires: net-tools,\nRequires: python3-distro
deb.dependency.list=openssl, net-tools, python (>= 2.6)
