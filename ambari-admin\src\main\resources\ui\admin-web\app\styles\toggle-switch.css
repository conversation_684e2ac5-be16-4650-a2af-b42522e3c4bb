/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.ats-switch {
  border-radius: 20px;
  height: 40px;
  min-width: 66px;
  width: 66px;
  position: relative;
  overflow: hidden;
  border-color: #EBECF1;
  border-width: 2px;
}

.ats-switch .knob {
  margin-left: 34px;
  border-radius: 20px;
  height: 32px;
  width: 32px;
  margin-top: 2px;
}

.ats-switch .switch-left,
.ats-switch .switch-right {
  position: absolute;
  right: -40px;
}

.ats-switch .switch-animate {
  height: 100%;
}

.ats-switch .switch-off {
  background-color: white;
}

.ats-switch .switch-on {
  background-color: #1EB475;
  left: -10%;
}

.ats-switch .switch-on .knob {
  background-color: white;
}

.ats-switch .switch-off .knob {
  background-color: #999;
}

.switch-option-label {
  padding-left: 5px;
  font-size: 14px;
}

.switch-inline-label {
  line-height: 40px;
}
