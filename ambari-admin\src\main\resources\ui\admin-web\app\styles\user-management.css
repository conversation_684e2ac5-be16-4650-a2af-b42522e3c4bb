/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#user-management .table th {
  vertical-align: baseline;
}

#user-management .search-box-row {
  margin-top: -1px;
}

#user-management .nav.nav-tabs {
  margin-bottom: 0;
}

#user-management .users-pane,
#user-management .groups-pane {
  margin-top: -35px;
}

#user-edit label,
#group-edit label {
  max-width: 150px;
  line-height: 34px;
}

#user-edit .one-row-value,
#group-edit .one-row-value {
  line-height: 20px;
}

#create-user-form .roles-label i,
#create-group-form .roles-label i,
#group-edit .roles-label i,
#user-edit .roles-label i {
  margin-right: -10px;
  cursor: pointer;
}
