<!--
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
-->

<div class="users-pane">
  <div class="clearfix">
    <ol class="breadcrumb pull-left">
      <li class="active">{{'common.loginActivities.loginActivities' | translate}}</li>
    </ol>
  </div>
  <hr>
  <ul class="nav nav-tabs">
    <li ng-class="{active: tab == 'loginMessage'}">
      <link-to route="loginActivities.loginMessage">{{'common.loginActivities.loginMessage' | translate}}</link-to>
    </li>
    <li ng-class="{active: tab == 'homeDirectory'}">
      <link-to route="loginActivities.homeDirectory">{{'common.loginActivities.homeDirectory' | translate}}</link-to>
    </li>
  </ul>

  <div class="tab-content">
    <div ng-switch = "tab">
      <div ng-switch-when = "loginMessage">
        <div ng-include = "'views/loginActivities/loginMessage.html'"></div>
      </div>

      <div ng-switch-when = "homeDirectory">
        <div ng-include = "'views/loginActivities/homeDirectory.html'"></div>
      </div>
    </div>
  </div>




</div>
