/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#stack-versions .table .col-small {
  width: 15%
}

#stack-versions .table .col-medium {
  width: 30%
}

#stack-versions .no-version-alert {
  text-align: center;
}
#stack-versions table {
  table-layout: fixed;
}
#stack-versions table .text-search-container {
  font-weight: normal;
  position: relative;
}
#stack-versions table .text-search-container .close {
  position: absolute;
  right: 15px;
  top: 40px;
}

#stack-versions table td > * {
  line-height: 24px;
}

.repo-table-title #name-label-adjust {
  width: 20.7%;
  padding-left:0px;
  right:5px;
}

.repo-table-title #repo-base-url-label {
  padding-left:0px;
  right:3px;
}

.verison-label-row .label {
  font-size: 100%;
}

.verison-label-row .btn {
  padding: 10px;
}

.verison-label-row.button-padding {
  padding: 4px;
}

.repo-name-label input {
  width: calc(100% - 30px);
  margin-right: 2px;
  display: inline-block;
}
