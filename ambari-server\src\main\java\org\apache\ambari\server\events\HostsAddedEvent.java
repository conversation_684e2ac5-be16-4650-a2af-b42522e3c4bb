/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.ambari.server.events;

import java.util.Set;

/**
 * The {@link HostsAddedEvent} is fired when the hosts are added to a cluster.
 */
public class HostsAddedEvent extends ClusterEvent {

  /**
   * The hosts' names.
   */
  protected final Set<String> m_hostNames;

  /**
   * Constructor.
   * @param clusterId
   * @param hostNames
   */
  public HostsAddedEvent(long clusterId, Set<String> hostNames) {
    super(AmbariEventType.HOST_ADDED, clusterId);
    m_hostNames = hostNames;
  }

  /**
   * Gets the hosts' names that the event belongs to.
   *
   * @return the hostName
   */
  public Set<String> getHostNames() {
    return m_hostNames;
  }
}
