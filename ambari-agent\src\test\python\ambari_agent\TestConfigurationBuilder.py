#!/usr/bin/env python3

"""
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

from mock.mock import MagicMock, patch
from unittest import TestCase

from ambari_agent.ConfigurationBuilder import ConfigurationBuilder
from ambari_agent.InitializerModule import InitializerModule


class TestConfigurationBuilder(TestCase):
  @patch(
    "ambari_agent.hostname.public_hostname",
    new=MagicMock(return_value="c6401.ambari.apache.org"),
  )
  def test_public_fqdn(self):
    initializer_module = InitializerModule()

    config_builder = ConfigurationBuilder(initializer_module)
    self.assertEqual("c6401.ambari.apache.org", config_builder.public_fqdn)
