<!--
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
-->

<div class="users-pane">
  <div class="clearfix">
    <div class="pull-right">
      <a href="#/remoteClusters/create" class="btn btn-default">
        {{'views.registerRemoteCluster' | translate}}
      </a>
    </div>
    <div class="search-box-button pull-right">
      <button class="btn btn-default" ng-click="toggleSearchBox()">
        <i class="fa fa-filter" aria-hidden="true"></i>
      </button>
      <div class="popup-arrow-up hide"></div>
    </div>
  </div>

  <div class="search-box-row hide">
    <combo-search suggestions="filters" filter-change="filterClusters" placeholder="Search"></combo-search>
  </div>

  <table class="table table-striped table-hover">
    <thead>
      <tr>
        <th class="col-sm-3">
          <div class="search-container">
            <label>{{'views.clusterName' | translate}}</label>
          </div>
        </th>
        <th class="col-sm-9">
          <label>{{'common.services' | translate}}</label>
        </th>
      </tr>
    </thead>
    <tbody>
    <tr ng-repeat="remoteCluster in remoteClusters  | filter : { isShowed: true }">
      <td class="col-sm-3"><a href="#/remoteClusters/{{remoteCluster.ClusterInfo.name}}/edit">{{ remoteCluster.clusterName }}</a></td>
      <td class="col-sm-9">
        <span ng-repeat="service in remoteCluster.ClusterInfo.services" ng-if="remoteCluster.ClusterInfo.services.length > 0">{{ service }}{{$last ? '' : ','}} </span>
        <span ng-if="remoteCluster.ClusterInfo.services.length == 0">--</span>
      </td>
    </tr>
    </tbody>

  </table>
  <div ng-if="isLoading" class="spinner-container">
    <i class="fa fa-2x fa-spinner fa-spin" aria-hidden="true"></i>
  </div>
  <div class="alert empty-table-alert col-sm-12" ng-show="!tableInfo.showed && !isLoading">
    {{'common.alerts.noRemoteClusterDisplay' | translate}}
  </div>
  <div class="col-sm-12 table-bar" ng-show="tableInfo.total >= minInstanceForPagination">
    <div class="pull-left filtered-info">
      <span>{{'common.filterInfo' | translate:{showed: tableInfo.showed, total: tableInfo.total, term: constants.groups} }}</span>
    </div>
    <div class="pull-right left-margin">
      <pagination class="paginator" total-items="tableInfo.filtered" max-size="pagination.maxVisiblePages" items-per-page="groupsPerPage" ng-model="pagination.currentPage" ng-change="pageChanged()"></pagination>
    </div>
    <div class="pull-right">
      <select class="form-control" ng-model="pagination.itemsPerPage" ng-change="resetPagination()" ng-options="currOption for currOption in [10, 25, 50, 100]"></select>
    </div>
  </div>

</div>

