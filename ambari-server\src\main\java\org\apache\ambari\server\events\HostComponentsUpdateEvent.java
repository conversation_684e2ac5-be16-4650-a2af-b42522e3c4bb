/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.ambari.server.events;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Contains list of updated hostcomponents. This update will be sent to all subscribed recipients.
 */
public class HostComponentsUpdateEvent extends STOMPEvent {

  @JsonProperty("hostComponents")
  private List<HostComponentUpdate> hostComponentUpdates = new ArrayList<>();

  public HostComponentsUpdateEvent(List<HostComponentUpdate> hostComponentUpdates) {
    super(Type.HOSTCOMPONENT);
    this.hostComponentUpdates = hostComponentUpdates;
  }

  public List<HostComponentUpdate> getHostComponentUpdates() {
    return hostComponentUpdates;
  }

  public void setHostComponentUpdates(List<HostComponentUpdate> hostComponentUpdates) {
    this.hostComponentUpdates = hostComponentUpdates;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;

    HostComponentsUpdateEvent that = (HostComponentsUpdateEvent) o;

    return hostComponentUpdates != null ? hostComponentUpdates.equals(that.hostComponentUpdates) : that.hostComponentUpdates == null;
  }

  @Override
  public int hashCode() {
    return hostComponentUpdates != null ? hostComponentUpdates.hashCode() : 0;
  }
}
