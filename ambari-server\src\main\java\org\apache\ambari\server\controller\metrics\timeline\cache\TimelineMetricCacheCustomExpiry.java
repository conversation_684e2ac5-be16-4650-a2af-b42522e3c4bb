/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.ambari.server.controller.metrics.timeline.cache;

import java.util.concurrent.TimeUnit;

import org.ehcache.ValueSupplier;
import org.ehcache.expiry.Duration;
import org.ehcache.expiry.Expiry;


public class TimelineMetricCacheCustomExpiry implements Expiry<TimelineAppMetricCacheKey, TimelineMetricsCacheValue> {

    private final Duration timeToLive;
    private final Duration timeToIdle;

    public TimelineMetricCacheCustomExpiry(java.time.Duration timeToLive, java.time.Duration timeToIdle) {
        this.timeToLive = convertJavaDurationToEhcacheDuration(timeToLive);
        this.timeToIdle = convertJavaDurationToEhcacheDuration(timeToIdle);
    }

    @Override
    public Duration getExpiryForCreation(TimelineAppMetricCacheKey key, TimelineMetricsCacheValue value) {
        return timeToLive;
    }

    @Override
    public Duration getExpiryForAccess(TimelineAppMetricCacheKey key, ValueSupplier<? extends TimelineMetricsCacheValue> value) {
        return timeToIdle;
    }

    @Override
    public Duration getExpiryForUpdate(TimelineAppMetricCacheKey key, ValueSupplier<? extends TimelineMetricsCacheValue> oldValue, TimelineMetricsCacheValue newValue) {
        return timeToLive;
    }

    private Duration convertJavaDurationToEhcacheDuration(java.time.Duration javaDuration) {
        return Duration.of(javaDuration.toNanos(), TimeUnit.NANOSECONDS);
    }
}