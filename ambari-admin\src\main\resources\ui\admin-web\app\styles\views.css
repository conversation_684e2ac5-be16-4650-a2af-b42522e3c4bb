/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#create-instance-form i {
  cursor: pointer;
}

#create-instance-form .modal-body {
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
}

#create-instance-form button.active {
  color: #333;
  background-color: #e6e6e6;
  border-color: #adadad;
}

input[type="checkbox"] + label {
  line-height: 18px;
}

.viewstatus {
  display: inline-block;
}

.viewstatus.pending {
  width: 12px;
  height: 12px;
  border: 2px solid black;
  border-radius: 50%;
  vertical-align: middle;
  position: relative;
}

.viewstatus.pending:before, .viewstatus.pending:after {
  content: '';
  position: absolute;
  left: 4px;
  top: 3px;
  width: 5px;
  height: 2px;
  background: black;
}

.viewstatus.pending:after {
  top: -3px;
  left: 3px;
  width: 2px;
  height: 2px;
  border-radius: 100%;
}

.viewstatus.pending:before {
  -webkit-transform-origin: 0% 50%;
  -moz-transform-origin: 0% 50%;
  -ms-transform-origin: 0% 50%;
  -o-transform-origin: 0% 50%;
  transform-origin: 0% 50%;

  animation: rotate 2.0s infinite linear;
  -webkit-animation: rotate 2.0s infinite linear;
}

.viewstatus.deploying {
  width: 17px;
  height: 12px;
  text-align: center;
  vertical-align: text-top;
}

.viewstatus.deploying > div {
  background: black;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: stretchdelay 1.2s infinite ease-in-out;
  animation: stretchdelay 1.2s infinite ease-in-out;
}

.viewstatus.deploying .rect2 {
  -webkit-animation-delay: -1.1s;
  animation-delay: -1.1s;
}

.viewstatus.deploying .rect3 {
  -webkit-animation-delay: -1.0s;
  animation-delay: -1.0s;
}

#views-table td {
  word-break: break-all;
}
