/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.ambari.server.events;

import java.util.SortedMap;

import org.apache.ambari.server.agent.stomp.dto.TopologyCluster;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Contains info about clusters topology update. This update will be sent to all subscribed recipients.
 * Is used to messaging to agents.
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TopologyAgentUpdateEvent extends TopologyUpdateEvent {
  public TopologyAgentUpdateEvent(SortedMap<String, TopologyCluster> clusters, String hash, UpdateEventType eventType) {
    super(Type.AGENT_TOPOLOGY, clusters, hash, eventType);
  }
}
