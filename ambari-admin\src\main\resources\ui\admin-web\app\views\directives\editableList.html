<!--
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
-->

<div class="editable-list-container well" ng-class="{'edit-mode' : editMode, 'disabled' : !editable}" ng-click="enableEditMode($event)">
  <div class="items-box">
    <ul class="items-list">
      <li class="item" ng-repeat="item in items | orderBy:identity">
        <span>
          <a href>{{item}}</a>
          <button ng-click="removeFromItems(item)" type="button" class="close">
            <span aria-hidden="true">&times;</span>
            <span class="sr-only">{{'common.controls.close' | translate}}</span>
          </button>
        </span>
      </li>
      <li class="item add-item-input" ng-class="{'has-error': invalidInput}" ng-show="editMode">
        <span contenteditable></span>
        <div class="typeahead-box" ng-show="typeahead.length != 0">
          <ul>
            <li ng-repeat="item in typeahead" ng-click="addItem(item)" ng-class="{'selected' : $index == selectedTypeahed}">{{item}}</li>
          </ul>
        </div>
      </li>
      <li class="item add-item" ng-show="!editMode && !items.length">{{'common.add' | translate:{term: resourceType} }}</li>
    </ul>
  </div>
  <div class="actions-panel" ng-show="editMode">
    <button class="btn btn-default btn-xs cancel" ng-click="cancel($event)">
      <span class="glyphicon glyphicon-remove cancel"></span>
    </button>
    <button class="btn btn-primary btn-xs" ng-click="save($event)">
      <span class="glyphicon glyphicon-ok"></span>
    </button>
  </div>
  <div class="pencil-box">
    <span class="glyphicon glyphicon-pencil"></span>
  </div>
</div>