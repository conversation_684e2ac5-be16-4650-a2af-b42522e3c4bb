{"name": "adminconsole", "version": "0.0.0", "dependencies": {}, "devDependencies": {"bower": "1.8.8", "gulp": "^3.8.8", "gulp-add-src": "^0.2.0", "gulp-autoprefixer": "0.0.7", "gulp-cache": "0.1.1", "gulp-clean": "0.2.4", "gulp-concat": "2.6.0", "gulp-filter": "0.4.1", "gulp-flatten": "0.0.2", "gulp-load-plugins": "0.5.0", "gulp-order": "1.1.1", "gulp-plumber": "1.1.0", "gulp-size": "0.3.0", "gulp-uglify": "0.2.1", "gulp-useref": "0.4.2", "http-server": "0.6.1", "jasmine-core": "^3.1.0", "karma": "^2.0.4", "karma-jasmine": "^1.1.2", "karma-ng-html2js-preprocessor": "^1.0.0", "karma-chrome-launcher": "3.2.0", "protractor": "1.0.0"}, "scripts": {"prestart": "npm install", "start": "http-server -a 0.0.0.0 -p 8000", "pretest": "npm install", "test": "node node_modules/karma/bin/karma start test/karma.conf.js", "test-single-run": "node node_modules/karma/bin/karma start test/karma.conf.js  --single-run", "preupdate-webdriver": "npm install", "update-webdriver": "webdriver-manager update", "preprotractor": "npm run update-webdriver", "protractor": "protractor test/protractor-conf.js", "update-index-async": "node -e \"require('shelljs/global'); sed('-i', /\\/\\/@@NG_LOADER_START@@[\\s\\S]*\\/\\/@@NG_LOADER_END@@/, '//@@NG_LOADER_START@@\\n' + cat('bower_components/angular-loader/angular-loader.min.js') + '\\n//@@NG_LOADER_END@@', 'app/index-async.html');\""}, "engines": {"node": ">=0.10.0"}}