<!--
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
-->
<div class="modal-header">
    <h3 class="modal-title">{{'versions.addVersion' | translate}}</h3>
</div>
<br>
<div class="clearfix register-version-options" ng-click="selectedLocalOption.index=1">
    <div class="option-radio-button checkbox">
        <input type="radio" id="upload-file" ng-model="selectedLocalOption.index" value="1">
        <label for="upload-file" class="option-label">
           {{'versions.uploadFile' | translate}}
        </label>
    </div>
    <div class="col-sm-7 choose-file-input">
        <input type="file" ng-model="option1.file" ng-class="selectedLocalOption.index!='1' ? 'disabled' : ''"
               onchange="angular.element(this).scope().onFileSelect(this)"/>
    </div>
</div>
<div class="clearfix register-version-options bottom-margin" ng-click="selectedLocalOption.index=2">
    <div class="option-radio-button checkbox">
        <input type="radio" id="entire-url" ng-model="selectedLocalOption.index" value="2">
        <label for="entire-url" class="option-label">
             {{'versions.enterURL' | translate}}
        </label>
    </div>
    <div class="col-sm-9">
        <div class="form-group {{option2.name}}" ng-class="{'has-error': option2.url.hasError }">
            <div class="stack-url-input">
                <input type="text" class="form-control" ng-model="option2.url"
                       placeholder="{{option2.placeholder}}" ng-class="selectedLocalOption.index!='2' ? 'disabled' : ''">
            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button class="btn btn-default" ng-click="cancel()">{{'common.controls.cancel' | translate}}</button>
    <button class="btn btn-primary" ng-model="button" ng-click="readVersionInfo()"
            ng-disabled="readInfoButtonDisabled()">{{'versions.readInfo' | translate}}</button>
</div>