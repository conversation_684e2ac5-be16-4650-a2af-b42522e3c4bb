<!--
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
-->
<div class="modal-header">
    <h3 class="modal-title">{{header}}</h3>
</div>
<div class="modal-body">
  <div class="default-body" ng-show="!isTempalte">{{body}}</div>
  <div ng-show="isTempalte" ng-include="body.url"></div>
  <br />
  <div ng-show="remoteInstances.length > 0">{{'common.messageInstanceAffected' | translate}}</div>
  <ul><li ng-repeat="instanceAffected in remoteInstances">{{instanceAffected}}</li></ul>
</div>
<div class="modal-footer">
    <button class="btn btn-default" ng-click="cancel()">{{cancelText}}</button>
    <button class="btn btn-primary" ng-click="ok()">{{confirmText}}</button>
</div>