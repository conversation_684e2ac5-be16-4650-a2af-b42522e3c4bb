<?xml version="1.0"?>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.1"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.1 http://maven.apache.org/xsd/assembly-1.1.1.xsd">
  <!--This 'all' id is not appended to the produced bundle because we do this:
    http://maven.apache.org/plugins/maven-assembly-plugin/faq.html#required-classifiers
  -->
  <formats>
    <format>dir</format>
    <format>tar.gz</format>
  </formats>
  <includeBaseDirectory>false</includeBaseDirectory>
  <!-- File sets. Syntax:
	  <fileSets>
	    <fileSet>
	      <useDefaultExcludes/>
	      <outputDirectory/>
	      <includes/>
	      <excludes/>
	      <fileMode/>
	      <directoryMode/>
	      <directory/>
	      <lineEnding/>
	      <filtered/>
	    </fileSet>
	  </fileSets>
  -->
  <fileSets>
    <fileSet>
      <directoryMode>755</directoryMode>
      <fileMode>755</fileMode>
      <directory>src/main/python/ambari_agent</directory>
      <outputDirectory>${agent.install.dir}</outputDirectory>
    </fileSet>
    <fileSet>
      <directoryMode>755</directoryMode>
      <fileMode>755</fileMode>
      <directory>${project.basedir}/../ambari-common/src/main/python/ambari_commons</directory>
      <outputDirectory>${ambari_commons.install.dir}</outputDirectory>
    </fileSet>
    <fileSet>
      <directoryMode>755</directoryMode>
      <fileMode>755</fileMode>
      <directory>${resourceManagementSrcLocation}</directory>
      <outputDirectory>${resource_management.install.dir}</outputDirectory>
    </fileSet>
    <fileSet>
      <directoryMode>755</directoryMode>
      <fileMode>755</fileMode>
      <directory>${project.basedir}/../ambari-common/src/main/python/ambari_jinja2/ambari_jinja2</directory>
      <outputDirectory>${jinja.install.dir}</outputDirectory>
      <excludes>
      	<exclude>**/testsuite/**</exclude>
      </excludes>
    </fileSet>
    <fileSet>
      <directoryMode>755</directoryMode>
      <fileMode>755</fileMode>
      <directory>${project.basedir}/../ambari-common/src/main/python/ambari_simplejson</directory>
      <outputDirectory>${simplejson.install.dir}</outputDirectory>
    </fileSet>
    <fileSet>
      <directoryMode>755</directoryMode>
      <fileMode>755</fileMode>
      <directory>${project.basedir}/../ambari-common/src/main/python/ambari_stomp</directory>
      <outputDirectory>${stomp.install.dir}</outputDirectory>
    </fileSet>
    <fileSet>
      <directoryMode>755</directoryMode>
      <fileMode>755</fileMode>
      <directory>${project.basedir}/../ambari-common/src/main/python/ambari_ws4py</directory>
      <outputDirectory>${ws4py.install.dir}</outputDirectory>
    </fileSet>
    <fileSet>
      <directoryMode>755</directoryMode>
      <fileMode>755</fileMode>
      <directory>${project.basedir}/../ambari-common/src/main/python/ambari_pbkdf2</directory>
      <outputDirectory>${pbkdf2.install.dir}</outputDirectory>
    </fileSet>
    <fileSet>
      <directoryMode>755</directoryMode>
      <fileMode>755</fileMode>
      <directory>${project.basedir}/../ambari-common/src/main/python/ambari_pyaes</directory>
      <outputDirectory>${pyaes.install.dir}</outputDirectory>
    </fileSet>
    <fileSet>
      <directoryMode>755</directoryMode>
      <fileMode>755</fileMode>
      <directory>src/examples</directory>
      <outputDirectory>${lib.dir}/examples</outputDirectory>
    </fileSet>
    <fileSet>
      <directoryMode>755</directoryMode>
      <directory>${empty.dir}</directory>
      <outputDirectory>${package.pid.dir}</outputDirectory>
	  <excludes>
	    <exclude>*/**</exclude>
	  </excludes>
    </fileSet>
    <fileSet>
      <directoryMode>777</directoryMode>
      <directory>${empty.dir}</directory>
      <outputDirectory>/var/lib/${project.artifactId}/tmp</outputDirectory>
	  <excludes>
	    <exclude>*/**</exclude>
	  </excludes>
    </fileSet>
    <fileSet>
      <directoryMode>700</directoryMode>
      <fileMode>700</fileMode>
      <directory>${empty.dir}</directory>
      <outputDirectory>/var/lib/${project.artifactId}/keys</outputDirectory>
	  <excludes>
	    <exclude>*/**</exclude>
	  </excludes>
    </fileSet>
    <fileSet>
      <directoryMode>755</directoryMode>
      <directory>${empty.dir}</directory>
      <outputDirectory>${package.log.dir}</outputDirectory>
	  <excludes>
	    <exclude>*/**</exclude>
	  </excludes>
    </fileSet>
    <fileSet>
      <directoryMode>755</directoryMode>
      <directory>${empty.dir}</directory>
      <outputDirectory>/var/lib/ambari-agent/lib</outputDirectory>
	  <excludes>
	    <exclude>*/**</exclude>
	  </excludes>
    </fileSet>
    <fileSet>
      <directoryMode>755</directoryMode>
      <fileMode>755</fileMode>
      <directory>${target.cache.dir}</directory>
      <outputDirectory>/var/lib/ambari-agent/cache</outputDirectory>
    </fileSet>
    <fileSet>
      <directoryMode>755</directoryMode>
      <fileMode>755</fileMode>
      <directory>${pluggableStackDefinitionOutput}/custom_actions</directory>
      <outputDirectory>/var/lib/ambari-agent/cache/custom_actions</outputDirectory>
    </fileSet>
  </fileSets>
  <!-- Single files. Syntax:
	  <files>
	    <file>
	      <source/>
	      <outputDirectory/>
	      <destName/>
	      <fileMode/>
	      <lineEnding/>
	      <filtered/>
	    </file>
	  </files>
  -->
  <files>
    <file>
      <fileMode>644</fileMode>
      <source>${basedir}/target/zkmigrator.jar</source>
      <outputDirectory>/var/lib/ambari-agent/tools</outputDirectory>
    </file>
    <file>
      <fileMode>644</fileMode>
      <source>${basedir}/target/jcepolicyinfo.jar</source>
      <outputDirectory>/var/lib/ambari-agent/tools</outputDirectory>
    </file>
    <file>
      <fileMode>755</fileMode>
      <source>conf/unix/ambari-sudo.sh</source>
      <outputDirectory>/var/lib/ambari-agent/</outputDirectory>
    </file>
    <file>
      <fileMode>644</fileMode>
      <source>conf/unix/ambari-agent.ini</source>
      <outputDirectory>/etc/ambari-agent/conf</outputDirectory>
    </file>
    <file>
      <fileMode>644</fileMode>
      <source>conf/unix/logging.conf.sample</source>
      <outputDirectory>/etc/ambari-agent/conf</outputDirectory>
    </file>
    <file>
      <fileMode>755</fileMode>
      <source>${basedir}/target/src/ambari-agent</source>
      <outputDirectory>/var/lib/ambari-agent/bin</outputDirectory>
    </file>
    <file>
      <fileMode>700</fileMode>
      <source>conf/unix/ambari-env.sh</source>
      <outputDirectory>/var/lib/ambari-agent</outputDirectory>
    </file>
    <file>
      <fileMode>700</fileMode>
      <source>conf/unix/install-helper.sh</source>
      <outputDirectory>/var/lib/ambari-agent</outputDirectory>
    </file>
    <file>
      <fileMode>700</fileMode>
      <source>conf/unix/upgrade_agent_configs.py</source>
      <outputDirectory>/var/lib/ambari-agent</outputDirectory>
    </file>
    <file>
      <fileMode>644</fileMode>
      <source>etc/init/ambari-agent.conf</source>
      <outputDirectory>/etc/init</outputDirectory>
    </file>
    <file>
      <fileMode>755</fileMode>
      <source>etc/init.d/ambari-agent</source>
      <outputDirectory>/etc/init.d</outputDirectory>
    </file>
    <file>
      <fileMode>644</fileMode>
      <source>${basedir}/target/src/version</source>
      <outputDirectory>/var/lib/${project.artifactId}/data</outputDirectory>
    </file>
  </files>
  <dependencySets>
    <dependencySet>
      <fileMode>644</fileMode>
      <outputDirectory>/var/lib/${project.artifactId}/cred/lib</outputDirectory>
      <unpack>false</unpack>
      <includes>
        <include>commons-cli:commons-cli</include>
        <include>commons-collections:commons-collections</include>
        <include>org.apache.commons:commons-configuration2</include>
        <include>org.apache.commons:commons-compress</include>
        <include>commons-io:commons-io</include>
        <include>commons-lang:commons-lang</include>
        <include>org.apache.commons:commons-lang3</include>
        <include>commons-logging:commons-logging</include>
        <include>com.google.guava:guava</include>
        <include>org.slf4j:slf4j-api</include>
        <include>org.apache.hadoop:hadoop-common</include>
        <include>org.apache.hadoop:hadoop-auth</include>
        <include>org.apache.hadoop.thirdparty:hadoop-shaded-guava</include>
        <include>com.fasterxml.woodstox:woodstox-core</include>
        <include>org.codehaus.woodstox:stax2-api</include>
      </includes>
    </dependencySet>
  </dependencySets>
</assembly>
